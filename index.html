<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Donance-S Pro Photo Activity</title>

    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📸</text></svg>">

    <!-- Croppie CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/croppie/2.6.5/croppie.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="logo">
                <i class="fas fa-camera-retro"></i>
                <h1>Donance-S Pro Photo Activity</h1>
            </div>
            <p class="subtitle">Create beautiful framed photos with professional quality</p>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Step 1: Upload Photo -->
            <section class="step-section" id="upload-section">
                <div class="step-header">
                    <div class="step-number">1</div>
                    <h2>Upload Your Photo</h2>
                </div>
                
                <div class="upload-area" id="upload-area">
                    <div class="upload-content">
                        <i class="fas fa-cloud-upload-alt upload-icon"></i>
                        <h3>Drag & Drop your photo here</h3>
                        <p>or click to browse</p>
                        <input type="file" id="photo-input" accept="image/*" hidden>
                        <button class="upload-btn" onclick="document.getElementById('photo-input').click()">
                            <i class="fas fa-plus"></i> Choose Photo
                        </button>
                    </div>
                </div>
            </section>

            <!-- Step 2: Crop Photo -->
            <section class="step-section" id="crop-section" style="display: none;">
                <div class="step-header">
                    <div class="step-number">2</div>
                    <h2>Crop Your Photo</h2>
                </div>
                
                <div class="crop-container">
                    <div class="crop-instructions">
                        <p><i class="fas fa-info-circle"></i>
                        <span class="desktop-instruction">Scroll to zoom, drag to move</span>
                        <span class="mobile-instruction">Pinch to zoom, drag to move, or use buttons below</span>
                        </p>
                    </div>
                    <div class="crop-area" id="crop-area"></div>
                    <div class="crop-controls">
                        <div class="zoom-controls">
                            <button class="btn btn-zoom" onclick="zoomOut()">
                                <i class="fas fa-search-minus"></i>
                            </button>
                            <span class="zoom-label">Zoom</span>
                            <button class="btn btn-zoom" onclick="zoomIn()">
                                <i class="fas fa-search-plus"></i>
                            </button>
                        </div>
                        <button class="btn btn-secondary" onclick="resetCrop()">
                            <i class="fas fa-undo"></i> Reset
                        </button>
                        <button class="btn btn-primary" onclick="applyCrop()">
                            <i class="fas fa-check"></i> Apply Crop
                        </button>
                    </div>
                </div>
            </section>

            <!-- Step 3: Generate Frame -->
            <section class="step-section" id="frame-section" style="display: none;">
                <div class="step-header">
                    <div class="step-number">3</div>
                    <h2>Generate Framed Photo</h2>
                </div>
                
                <div class="frame-container">
                    <div class="progress-container">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progress-fill"></div>
                        </div>
                        <div class="progress-text" id="progress-text">Preparing...</div>
                    </div>
                    
                    <div class="preview-container" id="preview-container" style="display: none;">
                        <div class="preview-header">
                            <h3><i class="fas fa-eye"></i> Preview Your Framed Photo</h3>
                            <p>Here's how your final photo will look:</p>
                        </div>
                        <div class="preview-image-container">
                            <canvas id="final-canvas"></canvas>
                        </div>
                        <div class="preview-info">
                            <div class="info-item">
                                <i class="fas fa-image"></i>
                                <span>High Quality PNG Format</span>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-palette"></i>
                                <span>Professional Donance-S Pro Frame</span>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-check-circle"></i>
                                <span>Ready for Download</span>
                            </div>
                        </div>
                        <div class="download-controls">
                            <button class="btn btn-success" onclick="downloadImage()">
                                <i class="fas fa-download"></i> Download Framed Photo
                            </button>
                            <button class="btn btn-secondary" onclick="startOver()">
                                <i class="fas fa-refresh"></i> Start Over
                            </button>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <p>&copy; 2025 Donance-S Pro Photo Activity. All rights reserved.</p>
        </footer>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Processing your photo...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/croppie/2.6.5/croppie.min.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
